{% extends 'inventory/base_inventory.html' %}
{% load static %}
{% load inventory_permission_tags %}

{% block title %}تقارير المخزون - نظام إدارة المخزن{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Report Styles */
    .report-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .report-card:hover {
        box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    }
    
    .report-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 1.25rem;
        border-radius: 10px 10px 0 0;
    }
    
    .report-title {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    
    .report-icon {
        margin-left: 10px;
        color: var(--primary-color);
    }
    
    .report-filters {
        background-color: rgba(var(--primary-color-rgb), 0.03);
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
    
    /* Chart containers */
    .chart-container {
        position: relative;
        height: 300px;
        margin: 1.5rem 0;
    }
    
    .stock-table th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
    }
    
    /* Value summary cards */
    .value-summary-card {
        background-color: var(--light-color);
        border-radius: 10px;
        padding: 1.25rem;
        height: 100%;
        transition: all 0.3s ease;
    }
    
    .value-summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }
    
    .summary-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .summary-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    /* Export buttons */
    .export-btn {
        border-radius: 20px;
        padding: 0.5rem 1rem;
    }
    
    .export-btn i {
        margin-left: 5px;
    }
    
    /* Table styling */
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
    
    /* For print layout */
    @media print {
        .nav-sidebar, 
        .report-filters,
        .export-btn,
        .navbar-top {
            display: none;
        }
        
        .main-content {
            margin: 0;
            width: 100%;
            padding: 0;
        }
        
        .report-card {
            box-shadow: none;
            border: 1px solid #ddd;
            break-inside: avoid;
        }
        
        .chart-container {
            height: 200px;
            page-break-inside: avoid;
        }
        
        body {
            font-size: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <!-- Enhanced Header -->
    <div class="enhanced-header">
        <div class="enhanced-header-content">
            <h1 class="enhanced-title">
                <i class="fas fa-chart-bar enhanced-title-icon"></i>
                <span>تقرير المخزون</span>
            </h1>
            <p class="enhanced-subtitle">تقرير شامل عن حالة المخزون والأصناف</p>
        </div>
    </div>

    <div class="content-section">
        <div class="row">
            <div class="col-12 mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                تقارير المخزون
            </h4>
            
            <div class="export-actions">
                <button onclick="window.print()" class="btn btn-outline-dark export-btn">
                    <i class="fas fa-print"></i> طباعة التقارير
                </button>
                <button class="btn btn-outline-success export-btn">
                    <i class="fas fa-file-excel"></i> تصدير إكسل
                </button>
            </div>
        </div>
        
        <!-- فلاتر التقارير -->
        <div class="card report-filters mb-4">
            <form method="get" action="{% url 'inventory:stock_report' %}" class="mb-0">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">التصنيف</label>
                        <select name="category" class="form-select">
                            <option value="">كل التصنيفات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"i" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">حالة المخزون</label>
                        <select name="stock_status" class="form-select">
                            <option value="">الكل</option>
                            <option value="low" {% if request.GET.stock_status == 'low' %}selected{% endif %}>تحت الحد الأدنى</option>
                            <option value="normal" {% if request.GET.stock_status == 'normal' %}selected{% endif %}>عادي</option>
                            <option value="out" {% if request.GET.stock_status == 'out' %}selected{% endif %}>نفذت الكمية</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label">الفترة</label>
                        <div class="input-group">
                            <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from|default:'' }}">
                            <span class="input-group-text">إلى</span>
                            <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to|default:'' }}">
                        </div>
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <div class="d-grid gap-2 w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i>
                                تطبيق
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- ملخص القيم -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="value-summary-card">
                    <div class="summary-value">{{ total_products }}</div>
                    <div class="summary-label">إجمالي عدد الأصناف</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="value-summary-card">
                    <div class="summary-value">{{ total_stock_value }} ج.م</div>
                    <div class="summary-label">إجمالي قيمة المخزون</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="value-summary-card">
                    <div class="summary-value">{{ low_stock_count }}</div>
                    <div class="summary-label">أصناف تحت الحد الأدنى</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="value-summary-card">
                    <div class="summary-value">{{ out_of_stock }}</div>
                    <div class="summary-label">أصناف نفذت الكمية</div>
                </div>
            </div>
        </div>
        
        <!-- الرسوم البيانية -->
        <div class="row g-4 mb-4">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="report-header">
                        <h5 class="report-title">
                            <i class="fas fa-chart-pie report-icon"></i>
                            توزيع المخزون حسب التصنيف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="report-header">
                        <h5 class="report-title">
                            <i class="fas fa-chart-line report-icon"></i>
                            حركة المخزون (آخر 30 يوم)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="stockMovementChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- باقي المخزون -->
        <div class="card report-card">
            <div class="report-header d-flex justify-content-between align-items-center">
                <h5 class="report-title">
                    <i class="fas fa-boxes report-icon"></i>
                    باقي المخزون
                </h5>
            </div>
            <div class="card-body p-0">
                {% if products %}
                <div class="table-responsive">
                    <table class="table table-hover stock-table mb-0">
                        <thead>
                            <tr>
                                <th>رقم الصنف</th>
                                <th>اسم الصنف</th>
                                <th>التصنيف</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>سعر الوحدة</th>
                                <th>القيمة الإجمالية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ product.product_id }}</td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category.name|default:"-" }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>{{ product.minimum_threshold }}</td>
                                <td>{{ product.unit_price }} ج.م</td>
                                <td>{{ product.total_value }} ج.م</td>
                                <td>
                                    {% if product.quantity <= 0 %}
                                    <span class="badge bg-danger">نفذت الكمية</span>
                                    {% elif product.quantity < product.minimum_threshold %}
                                    <span class="badge bg-warning text-dark">تحت الحد الأدنى</span>
                                    {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات متاحة للعرض</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<!-- Chart.js library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Category Distribution Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: [{% for cat in category_chart_data %}'{{ cat.name }}',{% endfor %}],
                datasets: [{
                    data: [{% for cat in category_chart_data %}{{ cat.count }},{% endfor %}],
                    backgroundColor: [
                        '#3f51b5', '#ff4081', '#4caf50', '#03a9f4', '#ffc107', 
                        '#ff5722', '#9c27b0', '#607d8b', '#795548', '#009688'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        rtl: true
                    }
                }
            }
        });
        
        // Stock Movement Chart
        const stockMovementCtx = document.getElementById('stockMovementChart').getContext('2d');
        new Chart(stockMovementCtx, {
            type: 'line',
            data: {
                labels: [{% for item in stock_movement_data %}'{{ item.date }}',{% endfor %}],
                datasets: [
                    {
                        label: 'كميات الإضافة',
                        data: [{% for item in stock_movement_data %}{{ item.added }},{% endfor %}],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true
                    },
                    {
                        label: 'كميات الصرف',
                        data: [{% for item in stock_movement_data %}{{ item.removed }},{% endfor %}],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
{% endblock %}

{% endblock %}
